namespace :subscriptions do
  desc "Fix subscriptions that are stuck in the 'incomplete' state by syncing them with Stripe"
  task fix_stuck: :environment do
    # Use an environment variable for a dry run to test without making changes
    is_dry_run = ENV['DRY_RUN'].present?

    if is_dry_run
      puts "DRY RUN: No changes will be saved to the database."
    end

    # Find subscriptions that are incomplete but have a Stripe ID
    stuck_subscriptions = Subscription.where(status: :incomplete).where.not(stripe_subscription_id: nil)

    if stuck_subscriptions.empty?
      puts "No stuck subscriptions found."
      next
    end

    puts "Found #{stuck_subscriptions.count} stuck subscription(s). Checking their status on Stripe..."

    stuck_subscriptions.each do |subscription|
      begin
        puts "\nProcessing local subscription ID: #{subscription.id} (Stripe ID: #{subscription.stripe_subscription_id})..."

        # 1. Fetch the subscription from Stripe to get the source of truth
        stripe_sub = Stripe::Subscription.retrieve(subscription.stripe_subscription_id)

        # 2. Check if the subscription is active on Stripe
        if stripe_sub.status == 'active'
          puts "  - Status on Stripe is 'active'. This subscription needs to be fixed."

          # 3. Find or create the corresponding plan
          price_data = stripe_sub.items.data[0].price
          plan = find_or_create_plan_from_price(price_data)

          if plan.nil?
            puts "  - ERROR: Could not find or create a plan for price ID #{price_data.id}. Skipping."
            next
          end
          
          puts "  - Plan found/created: #{plan.name} (ID: #{plan.id})"

          # 4. Prepare the attributes to update
          new_attributes = {
            plan: plan,
            stripe_status: 'active',
            status: :active,
            current_period_start: Time.at(stripe_sub.current_period_start),
            current_period_end: Time.at(stripe_sub.current_period_end),
            provider_data: stripe_sub.to_h
          }

          if is_dry_run
            puts "  - DRY RUN: Would update subscription with these attributes:"
            puts "    - status: :active"
            puts "    - stripe_status: 'active'"
            puts "    - current_period_end: #{Time.at(stripe_sub.current_period_end)}"
          else
            # 5. Update the local subscription record
            if subscription.update(new_attributes)
              puts "  - SUCCESS: Subscription #{subscription.id} has been updated to 'active'."
            else
              puts "  - ERROR: Failed to update subscription #{subscription.id}: #{subscription.errors.full_messages.join(', ')}"
            end
          end
        else
          puts "  - Status on Stripe is '#{stripe_sub.status}'. No action needed."
        end

      rescue Stripe::InvalidRequestError => e
        if e.http_status == 404
          puts "  - ERROR: Subscription #{subscription.stripe_subscription_id} not found on Stripe. It might have been deleted."
          # Optionally, mark as canceled locally
          # subscription.update(status: :canceled, stripe_status: 'not_found') unless is_dry_run
        else
          puts "  - ERROR: A Stripe API error occurred: #{e.message}"
        end
      rescue => e
        puts "  - ERROR: An unexpected error occurred for subscription #{subscription.id}: #{e.message}"
      end
    end

    puts "\nTask finished."
  end

  # Helper method to find or create a plan, adapted from the webhook job
  # This avoids duplicating code and ensures consistency
  def find_or_create_plan_from_price(price_data)
    plan = Plan.find_by(stripe_price_id: price_data.id)
    return plan if plan

    begin
      product = Stripe::Product.retrieve(price_data.product)
      plan_name = price_data.nickname.present? ? "#{product.name} - #{price_data.nickname}" : product.name

      Plan.find_or_create_by!(stripe_price_id: price_data.id) do |p|
        p.name = plan_name
        p.stripe_product_id = product.id
        p.price_cents = price_data.unit_amount
        p.interval = price_data.recurring&.interval || 'one_time'
        p.tier = determine_tier_from_product(product)
        p.metadata = product.metadata.to_h
        p.active = product.active
      end
    rescue => e
      puts "    - ERROR creating plan: #{e.message}"
      return nil
    end
  end

  def determine_tier_from_product(product)
    name = product.name&.downcase || ''
    metadata = product.metadata || {}

    if metadata['tier']
      metadata['tier']
    elsif name.include?('pilot')
      'pilot'
    elsif name.include?('standard')
      'standard'
    elsif name.include?('premium') || name.include?('pro')
      'premium'
    else
      'free'
    end
  end
end