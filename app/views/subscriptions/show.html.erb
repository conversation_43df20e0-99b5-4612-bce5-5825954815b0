<%# ABOUTME: Current subscription details page with management options %>
<%# ABOUTME: Displays plan info, billing dates, and provides cancel/reactivate/portal access %>
<div class="subscription-container">
  <div class="subscription-header">
    <h1><%= t('subscriptions.show.title', default: 'My Subscription') %></h1>
    <div class="header-actions">
      <%= link_to t('subscriptions.show.view_plans', default: 'View Plans'), subscriptions_path, class: 'btn btn-secondary' %>
    </div>
  </div>

  <% if @subscription %>
    <div class="subscription-details">
      <!-- Current Plan Card -->
      <div class="plan-card current-plan">
        <div class="plan-header">
          <h2><%= @subscription.plan.name %></h2>
          <span class="subscription-badge <%= @subscription.status %>">
            <%= t("subscriptions.status.#{@subscription.status}", default: @subscription.status.humanize) %>
          </span>
        </div>
        
        <div class="plan-info">
          <div class="info-row">
            <span class="label"><%= t('subscriptions.show.plan_type', default: 'Plan Type:') %></span>
            <span class="value"><%= @subscription.plan.tier.humanize %></span>
          </div>
          
          <div class="info-row">
            <span class="label"><%= t('subscriptions.show.price', default: 'Price:') %></span>
            <span class="value">
              <%= number_to_currency(@subscription.plan.price_cents / 100.0, unit: 'EUR') %>
              / <%= @subscription.plan.interval == 'month' ? t('subscriptions.billing.monthly') : t('subscriptions.billing.yearly') %>
            </span>
          </div>
          
          <% if @subscription.current_period_end %>
            <div class="info-row">
              <span class="label">
                <% if @subscription.cancel_at_period_end? %>
                  <%= t('subscriptions.show.cancels_on', default: 'Cancels on:') %>
                <% else %>
                  <%= t('subscriptions.show.next_billing', default: 'Next billing:') %>
                <% end %>
              </span>
              <span class="value"><%= @subscription.current_period_end.strftime('%B %d, %Y') %></span>
            </div>
          <% end %>
          
          <% if @subscription.trial_end && @subscription.trial_end > Time.current %>
            <div class="info-row">
              <span class="label"><%= t('subscriptions.show.trial_ends', default: 'Trial ends:') %></span>
              <span class="value"><%= @subscription.trial_end.strftime('%B %d, %Y') %></span>
            </div>
          <% end %>
        </div>

        <div class="plan-actions">
          <% if @subscription.payment_provider == 'stripe' && current_user.stripe_customer_id %>
            <%= link_to t('subscriptions.show.billing_portal', default: 'Manage Billing'), 
                        portal_subscriptions_path, 
                        method: :post,
                        class: 'btn btn-primary' %>
          <% end %>
          
          <% if @subscription.can_cancel? %>
            <%= button_to t('subscriptions.show.cancel_subscription', default: 'Cancel Subscription'), 
                          subscription_path,
                          method: :delete,
                          params: { reason: 'User requested' },
                          data: { 
                            confirm: t('subscriptions.show.cancel_confirm', 
                                      default: 'Are you sure you want to cancel your subscription? It will remain active until the end of the billing period.') 
                          },
                          class: 'btn btn-danger' %>
          <% elsif @subscription.can_reactivate? %>
            <%= button_to t('subscriptions.show.reactivate_subscription', default: 'Reactivate Subscription'), 
                          reactivate_subscription_path,
                          method: :post,
                          class: 'btn btn-success' %>
          <% end %>
        </div>
      </div>

      <!-- Recent Invoices -->
      <% if @invoices && @invoices.any? %>
        <div class="invoices-section">
          <h3><%= t('subscriptions.show.recent_invoices', default: 'Recent Invoices') %></h3>
          <div class="invoices-list">
            <% @invoices.each do |invoice| %>
              <div class="invoice-row">
                <span class="invoice-date">
                  <%= Time.at(invoice.created).strftime('%B %d, %Y') if invoice.created %>
                </span>
                <span class="invoice-amount">
                  <%= number_to_currency(invoice.amount_paid / 100.0, unit: invoice.currency.upcase == 'EUR' ? 'EUR' : invoice.currency.upcase) %>
                </span>
                <span class="invoice-status <%= invoice.status %>">
                  <%= invoice.status&.humanize %>
                </span>
                <% if invoice.invoice_pdf %>
                  <%= link_to t('subscriptions.show.download_invoice', default: 'Download'), 
                              invoice.invoice_pdf, 
                              target: '_blank',
                              class: 'btn btn-sm btn-outline' %>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>

      <!-- Upcoming Invoice -->
      <% if @upcoming_invoice %>
        <div class="upcoming-invoice-section">
          <h3><%= t('subscriptions.show.upcoming_invoice', default: 'Upcoming Invoice') %></h3>
          <div class="invoice-preview">
            <div class="info-row">
              <span class="label"><%= t('subscriptions.show.next_payment', default: 'Next payment:') %></span>
              <span class="value">
                <%= number_to_currency(@upcoming_invoice.amount_due / 100.0, unit: @upcoming_invoice.currency.upcase == 'EUR' ? 'EUR' : @upcoming_invoice.currency.upcase) %>
              </span>
            </div>
            <% if @upcoming_invoice.next_payment_attempt %>
              <div class="info-row">
                <span class="label"><%= t('subscriptions.show.payment_date', default: 'Payment date:') %></span>
                <span class="value">
                  <%= Time.at(@upcoming_invoice.next_payment_attempt).strftime('%B %d, %Y') %>
                </span>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="no-subscription">
      <p><%= t('subscriptions.show.no_subscription', default: "You don't have an active subscription.") %></p>
      <%= link_to t('subscriptions.show.view_plans_cta', default: 'View Available Plans'), 
                  subscriptions_path, 
                  class: 'btn btn-primary' %>
    </div>
  <% end %>
</div>

<style>
  .subscription-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
  }

  .subscription-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 1rem;
  }

  .subscription-header h1 {
    margin: 0;
    font-size: 1.8rem;
  }

  .current-plan {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
  }

  .plan-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .subscription-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .subscription-badge.active {
    background: #d4edda;
    color: #155724;
  }

  .subscription-badge.trialing {
    background: #d1ecf1;
    color: #0c5460;
  }

  .subscription-badge.canceled {
    background: #f8d7da;
    color: #721c24;
  }

  .subscription-badge.past_due {
    background: #fff3cd;
    color: #856404;
  }

  .plan-info {
    margin-bottom: 1.5rem;
  }

  .info-row {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .info-row .label {
    font-weight: 500;
    color: #666;
  }

  .info-row .value {
    color: #333;
  }

  .plan-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .invoices-section, .upcoming-invoice-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
  }

  .invoices-section h3, .upcoming-invoice-section h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.2rem;
  }

  .invoice-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .invoice-row:last-child {
    border-bottom: none;
  }

  .invoice-status {
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    font-size: 0.875rem;
  }

  .invoice-status.paid {
    background: #d4edda;
    color: #155724;
  }

  .no-subscription {
    text-align: center;
    padding: 3rem;
    background: #f8f9fa;
    border-radius: 8px;
  }

  .no-subscription p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 1.5rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s;
  }

  .btn-primary {
    background: #007bff;
    color: white;
  }

  .btn-primary:hover {
    background: #0056b3;
  }

  .btn-secondary {
    background: #6c757d;
    color: white;
  }

  .btn-secondary:hover {
    background: #545b62;
  }

  .btn-danger {
    background: #dc3545;
    color: white;
  }

  .btn-danger:hover {
    background: #c82333;
  }

  .btn-success {
    background: #28a745;
    color: white;
  }

  .btn-success:hover {
    background: #218838;
  }

  .btn-outline {
    background: transparent;
    color: #007bff;
    border: 1px solid #007bff;
  }

  .btn-outline:hover {
    background: #007bff;
    color: white;
  }

  .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }
</style>