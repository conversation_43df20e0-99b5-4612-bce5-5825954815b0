<%# ABOUTME: Plan selection page for new subscriptions with monthly/annual toggle %>
<%# ABOUTME: Reuses components from index page for consistent UI experience %>
<div class="subscription-container">
  <div class="subscription-header">
    <h1><%= t('subscriptions.new.title', default: 'Choose Your Plan') %></h1>
    <div class="header-actions">
      <% if current_user.active_subscription %>
        <%= link_to t('subscriptions.new.current_subscription', default: 'Current Subscription'), 
                    subscription_path, 
                    class: 'btn btn-secondary' %>
      <% end %>
    </div>
  </div>
  
  <% unless @has_active_premium %>
    <!-- Coupon Section -->
    <div class="coupon-section">
      <div class="coupon-input-group">
        <input type="text" 
               id="coupon-code-input" 
               class="coupon-code-input" 
               placeholder="<%= t('subscriptions.index.coupon_placeholder', default: 'Enter referral or coupon code') %>" 
               autocomplete="off">
        <button id="apply-coupon-btn" class="apply-coupon-btn">
          <%= t('subscriptions.index.apply_coupon', default: 'Apply') %>
        </button>
      </div>
      <div id="coupon-banner" class="coupon-banner" style="display: none;">
        <span id="coupon-message" class="banner-text"></span>
        <button id="remove-coupon-btn" class="remove-coupon-btn" style="display: none;">
          <%= t('subscriptions.coupon.remove', default: 'Remove') %>
        </button>
      </div>
    </div>

    <div class="subscription-plans">
      <% if @plans && @plans.any? %>
        <% @plans.group_by(&:tier).each do |tier, tier_plans| %>
          <div class="plan-card <%= tier %>-plan">
            <div class="plan-header">
              <div class="plan-title">
                <h2><%= tier.humanize %></h2>
                
                <% if tier_plans.length > 1 %>
                  <div class="billing-toggle-container">
                    <div class="billing-toggle">
                      <button class="toggle-option monthly-option active" data-period="monthly">
                        <%= t('subscriptions.billing.monthly') %>
                      </button>
                      <button class="toggle-option annual-option" data-period="annual">
                        <%= t('subscriptions.billing.yearly') %>
                      </button>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
            
            <% monthly_plan = tier_plans.find { |p| p.interval == 'month' } %>
            <% annual_plan = tier_plans.find { |p| p.interval == 'year' } %>
            <% display_plan = monthly_plan || annual_plan || tier_plans.first %>
            
            <div class="plan-price">
              <div id="regular-price-display-<%= tier %>">
                <div class="regular-price">
                  <span class="currency"><%= display_plan.price_cents > 0 ? 'EUR' : '' %></span>
                  <span class="amount" id="<%= tier %>-amount">
                    <%= display_plan.price_cents > 0 ? (display_plan.price_cents / 100).floor : 'Free' %>
                  </span>
                  <span class="period" id="<%= tier %>-period">
                    <%= display_plan.interval == 'month' ? '/ month' : display_plan.interval == 'year' ? '/ year' : '' %>
                  </span>
                </div>
                <!-- Discount preview -->
                <div id="discount-preview-<%= tier %>" class="discount-preview" style="display: none;">
                  <span class="discounted-price"></span>
                  <span class="original-price"></span>
                </div>
              </div>
            </div>
            
            <div class="plan-features">
              <% if tier == 'premium' %>
                <div class="feature-item included">
                  <div class="feature-icon"></div>
                  <span><%= t('subscriptions.plans.premium.features.all_free_features', default: 'Everything in Free') %></span>
                </div>
                <div class="feature-item included">
                  <div class="feature-icon"></div>
                  <span><%= t('subscriptions.plans.premium.features.unlimited_projects', default: 'Unlimited projects') %></span>
                </div>
                <div class="feature-item included">
                  <div class="feature-icon"></div>
                  <span><%= t('subscriptions.plans.premium.features.file_uploads', default: 'File uploads (25MB)') %></span>
                </div>
                <div class="feature-item included">
                  <div class="feature-icon"></div>
                  <span><%= t('subscriptions.plans.premium.features.full_network', default: 'Full network features') %></span>
                </div>
                <div class="feature-item included">
                  <div class="feature-icon"></div>
                  <span><%= t('subscriptions.plans.premium.features.priority_support', default: 'Priority support') %></span>
                </div>
              <% else %>
                <div class="feature-item included">
                  <div class="feature-icon"></div>
                  <span><%= t('subscriptions.plans.free.features.view_projects', default: 'View public projects') %></span>
                </div>
                <div class="feature-item included">
                  <div class="feature-icon"></div>
                  <span><%= t('subscriptions.plans.free.features.basic_network', default: 'Basic networking') %></span>
                </div>
                <div class="feature-item excluded">
                  <div class="feature-icon"></div>
                  <span><%= t('subscriptions.plans.free.features.no_create_projects', default: 'Cannot create projects') %></span>
                </div>
                <div class="feature-item excluded">
                  <div class="feature-icon"></div>
                  <span><%= t('subscriptions.plans.free.features.no_upload_files', default: 'Cannot upload files') %></span>
                </div>
              <% end %>
            </div>
            
            <div class="plan-action">
              <% if tier == 'premium' && display_plan.stripe_price_id %>
                <%= form_with url: subscriptions_path, local: true, class: 'subscription-form' do |f| %>
                  <%= hidden_field_tag :price_id, monthly_plan&.stripe_price_id, class: "price-id-field" %>
                  <%= hidden_field_tag :coupon_code, '', class: "coupon-code-field" %>
                  <button type="submit" 
                          class="plan-button premium-button" 
                          data-monthly-price-id="<%= monthly_plan&.stripe_price_id %>"
                          data-annual-price-id="<%= annual_plan&.stripe_price_id %>"
                          data-monthly-price="<%= monthly_plan ? (monthly_plan.price_cents / 100) : 0 %>"
                          data-annual-price="<%= annual_plan ? (annual_plan.price_cents / 100) : 0 %>">
                    <%= t('subscriptions.plans.premium.button', default: 'Get Premium') %>
                  </button>
                <% end %>
              <% elsif tier != 'premium' %>
                <button class="plan-button free-button" disabled>
                  <%= t('subscriptions.plans.free.button', default: 'Current Plan') %>
                </button>
              <% end %>
            </div>
          </div>
        <% end %>
      <% else %>
        <p><%= t('subscriptions.new.no_plans', default: 'No plans available at this time.') %></p>
      <% end %>
    </div>
  <% else %>
    <div class="existing-subscription-notice">
      <p><%= t('subscriptions.new.already_subscribed', default: 'You already have an active subscription.') %></p>
      <%= link_to t('subscriptions.new.manage_subscription', default: 'Manage Your Subscription'), 
                  subscription_path, 
                  class: 'btn btn-primary' %>
    </div>
  <% end %>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Get DOM elements
  const toggleOptions = document.querySelectorAll('.toggle-option');
  const premiumButton = document.querySelector('.premium-button');
  const priceIdField = document.querySelector('.price-id-field');
  const couponCodeField = document.querySelector('.coupon-code-field');
  const couponInput = document.getElementById('coupon-code-input');
  const applyCouponBtn = document.getElementById('apply-coupon-btn');
  const couponBanner = document.getElementById('coupon-banner');
  const couponMessage = document.getElementById('coupon-message');
  const removeCouponBtn = document.getElementById('remove-coupon-btn');
  
  let appliedCouponCode = null;
  let appliedCouponData = null;
  let currentPeriod = 'monthly';
  
  // Handle billing period toggle
  toggleOptions.forEach(option => {
    option.addEventListener('click', function() {
      toggleOptions.forEach(opt => opt.classList.remove('active'));
      this.classList.add('active');
      
      currentPeriod = this.dataset.period;
      updatePricing(currentPeriod);
    });
  });
  
  function updatePricing(period) {
    if (!premiumButton) return;
    
    const monthlyPriceId = premiumButton.dataset.monthlyPriceId;
    const annualPriceId = premiumButton.dataset.annualPriceId;
    const monthlyPrice = parseFloat(premiumButton.dataset.monthlyPrice);
    const annualPrice = parseFloat(premiumButton.dataset.annualPrice);
    
    const amountSpan = document.getElementById('premium-amount');
    const periodSpan = document.getElementById('premium-period');
    
    if (period === 'annual' && annualPriceId) {
      if (priceIdField) priceIdField.value = annualPriceId;
      if (amountSpan) amountSpan.textContent = Math.floor(annualPrice);
      if (periodSpan) periodSpan.textContent = '/ year';
    } else if (monthlyPriceId) {
      if (priceIdField) priceIdField.value = monthlyPriceId;
      if (amountSpan) amountSpan.textContent = Math.floor(monthlyPrice);
      if (periodSpan) periodSpan.textContent = '/ month';
    }
    
    // Reapply coupon if exists
    if (appliedCouponData) {
      applyDiscountPreview(appliedCouponData, period === 'annual' ? annualPrice : monthlyPrice);
    }
  }
  
  // Coupon handling
  if (applyCouponBtn) {
    applyCouponBtn.addEventListener('click', function() {
      const code = couponInput.value.trim();
      if (!code) return;
      
      // Call validate endpoint
      fetch('<%= validate_coupon_subscriptions_path %>', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({ code: code })
      })
      .then(response => response.json())
      .then(data => {
        if (data.valid) {
          appliedCouponCode = data.code;
          appliedCouponData = data;
          
          // Update UI
          couponMessage.textContent = data.message;
          couponBanner.style.display = 'block';
          couponBanner.classList.add('success');
          couponBanner.classList.remove('error');
          removeCouponBtn.style.display = 'inline-block';
          couponInput.disabled = true;
          applyCouponBtn.disabled = true;
          
          // Set coupon code in form
          if (couponCodeField) couponCodeField.value = code;
          
          // Show discount preview
          const currentPrice = currentPeriod === 'annual' ? 
            parseFloat(premiumButton?.dataset.annualPrice || 0) : 
            parseFloat(premiumButton?.dataset.monthlyPrice || 0);
          applyDiscountPreview(data, currentPrice);
        } else {
          couponMessage.textContent = data.message || 'Invalid coupon code';
          couponBanner.style.display = 'block';
          couponBanner.classList.add('error');
          couponBanner.classList.remove('success');
          removeCouponBtn.style.display = 'none';
        }
      })
      .catch(error => {
        console.error('Error validating coupon:', error);
        couponMessage.textContent = 'Error validating coupon';
        couponBanner.style.display = 'block';
        couponBanner.classList.add('error');
      });
    });
  }
  
  if (removeCouponBtn) {
    removeCouponBtn.addEventListener('click', function() {
      appliedCouponCode = null;
      appliedCouponData = null;
      couponInput.value = '';
      couponInput.disabled = false;
      applyCouponBtn.disabled = false;
      couponBanner.style.display = 'none';
      if (couponCodeField) couponCodeField.value = '';
      
      // Remove discount preview
      document.querySelectorAll('.discount-preview').forEach(el => {
        el.style.display = 'none';
      });
    });
  }
  
  function applyDiscountPreview(couponData, originalPrice) {
    const discountPreview = document.getElementById('discount-preview-premium');
    if (!discountPreview) return;
    
    let discountedPrice = originalPrice;
    
    if (couponData.discount) {
      if (couponData.discount.type === 'percentage') {
        discountedPrice = originalPrice * (1 - couponData.discount.value / 100);
      } else if (couponData.discount.type === 'amount') {
        discountedPrice = Math.max(0, originalPrice - couponData.discount.value);
      }
    }
    
    const discountedSpan = discountPreview.querySelector('.discounted-price');
    const originalSpan = discountPreview.querySelector('.original-price');
    
    if (discountedSpan) {
      discountedSpan.textContent = `EUR ${Math.floor(discountedPrice)}`;
    }
    if (originalSpan) {
      originalSpan.textContent = `EUR ${Math.floor(originalPrice)}`;
      originalSpan.style.textDecoration = 'line-through';
    }
    
    discountPreview.style.display = 'flex';
  }
  
  // Handle enter key in coupon input
  if (couponInput) {
    couponInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        applyCouponBtn.click();
      }
    });
  }
});
</script>

<style>
  /* Reuse styles from index page */
  .subscription-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
  }

  .subscription-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 1rem;
  }

  .subscription-header h1 {
    margin: 0;
    font-size: 1.8rem;
  }

  .coupon-section {
    margin-bottom: 2rem;
  }

  .coupon-input-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .coupon-code-input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
  }

  .apply-coupon-btn {
    padding: 0.75rem 1.5rem;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .apply-coupon-btn:hover {
    background: #0056b3;
  }

  .apply-coupon-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .coupon-banner {
    padding: 0.75rem 1rem;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .coupon-banner.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  .coupon-banner.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }

  .remove-coupon-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 4px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
  }

  .subscription-plans {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
  }

  .plan-card {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
  }

  .plan-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .premium-plan {
    border: 2px solid #007bff;
  }

  .plan-header {
    margin-bottom: 1.5rem;
  }

  .plan-title h2 {
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
  }

  .billing-toggle-container {
    margin-top: 1rem;
  }

  .billing-toggle {
    display: flex;
    gap: 0;
    background: #f0f0f0;
    border-radius: 4px;
    padding: 2px;
  }

  .toggle-option {
    flex: 1;
    padding: 0.5rem;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: background 0.2s;
    border-radius: 3px;
  }

  .toggle-option.active {
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .plan-price {
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .regular-price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.25rem;
  }

  .regular-price .currency {
    font-size: 1.2rem;
    color: #666;
  }

  .regular-price .amount {
    font-size: 2.5rem;
    font-weight: bold;
    color: #333;
  }

  .regular-price .period {
    font-size: 1rem;
    color: #666;
  }

  .discount-preview {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 0.5rem;
  }

  .discounted-price {
    font-size: 2rem;
    font-weight: bold;
    color: #28a745;
  }

  .original-price {
    font-size: 1.2rem;
    color: #999;
  }

  .plan-features {
    margin-bottom: 1.5rem;
  }

  .feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
  }

  .feature-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }

  .feature-item.included .feature-icon {
    background: #28a745;
    position: relative;
  }

  .feature-item.included .feature-icon::after {
    content: "✓";
    position: absolute;
    color: white;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
  }

  .feature-item.excluded .feature-icon {
    background: #dc3545;
    position: relative;
  }

  .feature-item.excluded .feature-icon::after {
    content: "×";
    position: absolute;
    color: white;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 14px;
  }

  .plan-action {
    margin-top: 1.5rem;
  }

  .subscription-form {
    width: 100%;
  }

  .plan-button {
    width: 100%;
    padding: 1rem;
    font-size: 1.1rem;
    font-weight: 500;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .premium-button {
    background: #007bff;
    color: white;
  }

  .premium-button:hover {
    background: #0056b3;
  }

  .free-button {
    background: #6c757d;
    color: white;
    cursor: not-allowed;
    opacity: 0.5;
  }

  .existing-subscription-notice {
    text-align: center;
    padding: 3rem;
    background: #f8f9fa;
    border-radius: 8px;
  }

  .existing-subscription-notice p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 1.5rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s;
  }

  .btn-primary {
    background: #007bff;
    color: white;
  }

  .btn-primary:hover {
    background: #0056b3;
  }

  .btn-secondary {
    background: #6c757d;
    color: white;
  }

  .btn-secondary:hover {
    background: #545b62;
  }
</style>