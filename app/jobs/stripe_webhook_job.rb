# ABOUTME: Processes Stripe webhook events asynchronously with retry logic
# ABOUTME: Handles subscription lifecycle events and payment status updates
class StripeWebhookJob < ApplicationJob
  queue_as :default
  
  # Retry with exponential backoff for resilience
  retry_on StandardError, wait: :exponentially_longer, attempts: 5
  
  def perform(event_data)
    event = Stripe::Event.construct_from(event_data)
    
    Rails.logger.info "Processing Stripe webhook: #{event['type']}"
    
    ActiveRecord::Base.transaction do
      case event['type']
      when 'checkout.session.completed'
        handle_checkout_session_completed(event)
      when 'customer.subscription.created'
        handle_subscription_created(event)
      when 'customer.subscription.updated'
        handle_subscription_updated(event)
      when 'customer.subscription.deleted'
        handle_subscription_canceled(event)
      when 'customer.subscription.trial_will_end'
        handle_trial_ending(event)
      when 'invoice.payment_succeeded'
        handle_payment_succeeded(event)
      when 'invoice.payment_failed'
        handle_payment_failed(event)
      when 'customer.created'
        handle_customer_created(event)
      when 'customer.updated'
        handle_customer_updated(event)
      else
        Rails.logger.info "Unhandled webhook event type: #{event['type']}"
      end
    end
  end
  
  private
  
  def handle_checkout_session_completed(event)
    session = event['data']['object']
    subscription_id = session['client_reference_id']

    return unless subscription_id # Not a session we created

    subscription = Subscription.find_by(id: subscription_id)
    return log_missing_subscription(subscription_id) unless subscription

    # Update the incomplete subscription with Stripe IDs
    subscription.update!(
      stripe_subscription_id: session['subscription'],
      stripe_customer_id: session['customer']
    )

    # If payment succeeded immediately (no 3D Secure), update status to active
    if session['payment_status'] == 'paid' && session['status'] == 'complete'
      stripe_sub = Stripe::Subscription.retrieve(session['subscription'])

      if stripe_sub.status == 'active'
        plan = find_or_create_plan_from_price(stripe_sub.items.data[0].price)

        subscription.update!(
          plan: plan,
          stripe_status: 'active',
          status: :active,
          current_period_start: Time.at(stripe_sub.current_period_start),
          current_period_end: Time.at(stripe_sub.current_period_end),
          provider_data: stripe_sub.to_h
        )

        Rails.logger.info "Subscription #{subscription.id} activated immediately after successful payment"
      end
    end

    Rails.logger.info "Updated subscription #{subscription.id} with Stripe IDs from checkout session"
  end
  
  def handle_subscription_created(event)
    subscription_data = event['data']['object']

    # This now finds the record updated by checkout.session.completed
    subscription = Subscription.find_by(stripe_subscription_id: subscription_data['id'])

    # If already active (set by checkout.session.completed), skip update
    if subscription && subscription.active?
      Rails.logger.info "Subscription #{subscription.id} already active, skipping duplicate update"
      return
    end

    if subscription.nil?
      # Fallback: If no subscription exists with this Stripe ID, try old webhook-driven flow
      # This handles edge cases or manual subscriptions created outside our checkout flow
      user = find_user_by_stripe_customer(subscription_data['customer'])
      return log_missing_user(subscription_data['customer']) unless user
      
      # Find the plan based on the price ID
      plan = find_or_create_plan_from_price(subscription_data['items']['data'][0]['price'])
      
      # Create new subscription record
      subscription = user.subscriptions.create!(
        plan: plan,
        stripe_subscription_id: subscription_data['id'],
        stripe_customer_id: subscription_data['customer'],
        stripe_status: subscription_data['status'],
        status: map_stripe_status(subscription_data['status'], subscription_data['cancel_at_period_end']),
        # Add safe navigation and defaults
        current_period_start: subscription_data['current_period_start'] ? 
          Time.at(subscription_data['current_period_start']) : Time.current,
        current_period_end: subscription_data['current_period_end'] ? 
          Time.at(subscription_data['current_period_end']) : (plan.interval == 'year' ? 1.year.from_now : 1.month.from_now),
        trial_start: subscription_data['trial_start'] ? 
          Time.at(subscription_data['trial_start']) : nil,
        trial_end: subscription_data['trial_end'] ? 
          Time.at(subscription_data['trial_end']) : nil,
        cancel_at_period_end: subscription_data['cancel_at_period_end'] || false,
        payment_provider: :stripe,
        provider_data: subscription_data
      )
      
      Rails.logger.info "Created new subscription #{subscription.id} for user #{user.id} (fallback flow)"
    else
      # Update the existing incomplete record with full details and set status to active
      plan = find_or_create_plan_from_price(subscription_data['items']['data'][0]['price'])
      
      subscription.update!(
        plan: plan,
        stripe_status: subscription_data['status'],
        status: map_stripe_status(subscription_data['status'], subscription_data['cancel_at_period_end']),
        current_period_start: subscription_data['current_period_start'] ? 
          Time.at(subscription_data['current_period_start']) : Time.current,
        current_period_end: subscription_data['current_period_end'] ? 
          Time.at(subscription_data['current_period_end']) : (plan.interval == 'year' ? 1.year.from_now : 1.month.from_now),
        trial_start: subscription_data['trial_start'] ? 
          Time.at(subscription_data['trial_start']) : nil,
        trial_end: subscription_data['trial_end'] ? 
          Time.at(subscription_data['trial_end']) : nil,
        cancel_at_period_end: subscription_data['cancel_at_period_end'] || false,
        provider_data: subscription_data
      )
      
      Rails.logger.info "Updated subscription #{subscription.id} status from incomplete to #{subscription.status}"
    end
    
    # Handle incomplete subscriptions (3D Secure, bank verification, etc.)
    handle_incomplete_subscription(subscription) if subscription.incomplete?
  end
  
  def handle_subscription_updated(event)
    subscription_data = event['data']['object']
    subscription = Subscription.find_by(stripe_subscription_id: subscription_data['id'])
    
    return log_missing_subscription(subscription_data['id']) unless subscription
    
    # Update subscription with new data (with nil-safe handling)
    subscription.update!(
      stripe_status: subscription_data['status'],
      status: Subscription.map_stripe_status(subscription_data['status'], subscription_data['cancel_at_period_end']),
      current_period_start: subscription_data['current_period_start'] ? 
        Time.at(subscription_data['current_period_start']) : subscription.current_period_start,
      current_period_end: subscription_data['current_period_end'] ? 
        Time.at(subscription_data['current_period_end']) : subscription.current_period_end,
      trial_end: subscription_data['trial_end'] ? 
        Time.at(subscription_data['trial_end']) : nil,
      cancel_at_period_end: subscription_data['cancel_at_period_end'] || false,
      canceled_at: subscription_data['canceled_at'] ? 
        Time.at(subscription_data['canceled_at']) : nil,
      provider_data: subscription_data
    )
    
    # Update plan if price changed
    # Correctly access nested attributes on the StripeObject
    if subscription_data.items.data[0].price.id != subscription.plan&.stripe_price_id
      plan = find_or_create_plan_from_price(subscription_data.items.data[0].price)
      subscription.update!(plan: plan)
    end
    
    # Handle incomplete subscriptions (3D Secure, bank verification, etc.)
    handle_incomplete_subscription(subscription) if subscription.incomplete?
    
    Rails.logger.info "Updated subscription #{subscription.id}"
  end
  
  def handle_subscription_canceled(event)
    subscription_data = event['data']['object']
    subscription = Subscription.find_by(stripe_subscription_id: subscription_data['id'])
    
    return log_missing_subscription(subscription_data['id']) unless subscription
    
    subscription.update!(
      stripe_status: 'canceled',
      status: :canceled,
      canceled_at: subscription_data['canceled_at'] ? 
        Time.at(subscription_data['canceled_at']) : Time.current,
      ended_at: Time.current,
      provider_data: subscription_data
    )
    
    # User's active subscription is now canceled
    # The status update above handles this
    
    Rails.logger.info "Canceled subscription #{subscription.id}"
  end
  
  def handle_trial_ending(event)
    subscription_data = event['data']['object']
    subscription = Subscription.find_by(stripe_subscription_id: subscription_data['id'])
    
    return log_missing_subscription(subscription_data['id']) unless subscription
    
    # TODO: Send notification email about trial ending
    # UserMailer.trial_ending_notification(subscription.user).deliver_later
    
    Rails.logger.info "Trial ending notification logged for subscription #{subscription.id}"
  end
  
  def handle_payment_succeeded(event)
    invoice_data = event['data']['object']
    
    return unless invoice_data['subscription'] # Skip one-time payments
    
    subscription = Subscription.find_by(stripe_subscription_id: invoice_data['subscription'])
    return log_missing_subscription(invoice_data['subscription']) unless subscription
    
    # Update subscription status to active if it was past_due or unpaid
    if subscription.needs_payment?
      subscription.update!(
        status: :active,
        stripe_status: 'active'
      )
    end
    
    # Log payment for analytics
    Rails.logger.info "Payment succeeded for subscription #{subscription.id}"
  end
  
  def handle_payment_failed(event)
    invoice_data = event['data']['object']
    
    return unless invoice_data['subscription']
    
    subscription = Subscription.find_by(stripe_subscription_id: invoice_data['subscription'])
    return log_missing_subscription(invoice_data['subscription']) unless subscription
    
    # Update subscription status based on retry attempts
    subscription.update!(
      status: :past_due,
      stripe_status: 'past_due'
    )
    
    # TODO: Send payment failure notification
    # UserMailer.payment_failed_notification(subscription.user).deliver_later
    
    Rails.logger.info "Payment failed for subscription #{subscription.id}"
  end
  
  def handle_customer_created(event)
    customer_data = event['data']['object']
    
    # If customer has metadata with user_id, link them
    if customer_data['metadata'] && customer_data['metadata']['user_id']
      user = User.find_by(id: customer_data['metadata']['user_id'])
      if user && user.stripe_customer_id.blank?
        user.update!(stripe_customer_id: customer_data['id'])
        Rails.logger.info "Linked Stripe customer #{customer_data['id']} to user #{user.id}"
      end
    end
  end
  
  def handle_customer_updated(event)
    customer_data = event['data']['object']
    user = User.find_by(stripe_customer_id: customer_data['id'])
    
    return unless user
    
    # Update user email if changed in Stripe
    if customer_data['email'] && customer_data['email'] != user.email
      Rails.logger.info "Stripe email mismatch for user #{user.id}: #{user.email} vs #{customer_data['email']}"
    end
  end
  
  def find_user_by_stripe_customer(stripe_customer_id)
    User.find_by(stripe_customer_id: stripe_customer_id)
  end
  
  def find_or_create_plan_from_price(price_data)
    # The most reliable way to find a plan is by its unique Stripe Price ID.
    plan = Plan.find_by(stripe_price_id: price_data['id'])
    return plan if plan

    # If the plan does not exist locally, we must create it.
    # This happens the first time someone subscribes to a new price (e.g., a special offer).
    product = Stripe::Product.retrieve(price_data.product)

    # Construct a descriptive and unique name for our local plan record.
    # We use the price's nickname to differentiate it from the standard plan.
    # e.g., "Unlisters Standard - Early Bird Deal" or just "Unlisters Standard" if nickname is blank.
    plan_name = if price_data.nickname.present?
                  "#{product.name} - #{price_data.nickname}"
                else
                  product.name
                end

    # Use find_or_create_by! to be absolutely certain we don't create duplicates
    # in the case of a race condition between two webhooks for the same new plan.
    Plan.find_or_create_by!(stripe_price_id: price_data.id) do |p|
      p.name = plan_name
      p.stripe_product_id = product.id
      p.price_cents = price_data.unit_amount
      # Correctly access nested object attributes; avoid .dig on StripeObject
      p.interval = price_data.recurring&.interval || 'one_time'
      p.tier = determine_tier_from_product(product)
      p.metadata = product.metadata.to_h
      p.active = product.active
    end
  end
  
  def determine_tier_from_product(product)
    # Determine tier based on product name or metadata
    # Use safe navigation (&.) to prevent errors if name is nil
    name = product.name&.downcase || ''
    metadata = product.metadata || {}

    if metadata['tier']
      metadata['tier']
    elsif name.include?('pilot')
      'pilot'
    elsif name.include?('standard')
      'standard'
    elsif name.include?('premium') || name.include?('pro')
      'premium'
    else
      'free'
    end
  end
  
  def map_stripe_status(stripe_status, cancel_at_period_end = false)
    Subscription.map_stripe_status(stripe_status, cancel_at_period_end)
  end
  
  def handle_incomplete_subscription(subscription)
    # Send notification to user about incomplete subscription
    Rails.logger.warn "Subscription #{subscription.id} is incomplete - requires additional verification"
    
    # TODO: Send email notification to user with instructions
    # UserMailer.incomplete_subscription(subscription).deliver_later
    
    # For now, log the issue for admin attention
    Rails.logger.error "ACTION REQUIRED: Subscription #{subscription.id} for user #{subscription.user_id} " \
                       "is incomplete and requires customer action. Status: #{subscription.stripe_status}"
    
    # Optionally, you could also create an admin notification or alert
    # AdminNotification.create!(
    #   title: "Incomplete Subscription",
    #   message: "Subscription #{subscription.id} requires attention",
    #   urgency: :high
    # )
  end
  
  def log_missing_user(stripe_customer_id)
    Rails.logger.error "No user found for Stripe customer: #{stripe_customer_id}"
    # TODO: Consider creating orphaned customer record for recovery
  end
  
  def log_missing_subscription(stripe_subscription_id)
    Rails.logger.error "No subscription found for Stripe ID: #{stripe_subscription_id}"
    # TODO: Consider fetching from Stripe API for recovery
  end
end